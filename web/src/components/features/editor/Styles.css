.editor-text-bold {
  font-weight: bold;
}

.editor-text-italic {
  font-style: italic;
}

.editor-text-underline {
  text-decoration: underline;
}

.editor-text-strikethrough {
  text-decoration: line-through;
}

.editor-text-underlineStrikethrough {
  text-decoration: underline line-through;
}

.editor-heading-h1 {
  font-size: 24px;
}

.editor-heading-h2 {
  font-size: 20px;
}

.editor-list-ol {
  padding: 0;
  margin: 0;
  margin-left: 16px;
}

.editor-list-ol > .editor-listitem {
  list-style: decimal;
}

.editor-list-ul {
  padding: 0;
  margin: 0;
  margin-left: 16px;
  list-style-type: disc;
}

.editor-list-ul > .editor-listitem {
  list-style: disc;
}

.editor-listitem {
  margin: 8px 0 8px 0;
}

.editor-quote {
  margin: 0;
  margin-left: 20px;
  font-size: 15px;
  color: var(--sidebar-ring);
  border-left-color: var(--sidebar-foreground);
  border-left-width: 4px;
  border-left-style: solid;
  padding-left: 16px;
}

.editor-content-editable {
  scrollbar-color: var(--sidebar-foreground) var(--sidebar-foreground);
}
