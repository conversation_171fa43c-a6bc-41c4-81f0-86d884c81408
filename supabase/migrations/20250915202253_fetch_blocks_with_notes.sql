set check_function_bodies = on;

DROP FUNCTION sp_fetch_folders(
  _campaign_id UUID,
  _user_id UUID,
  _include_notes BOOLEAN,
  _include_deleted BO<PERSON>EAN
);

CREATE FUNCTION sp_fetch_folders(
  _campaign_id UUID,
  _user_id UUID DEFAULT NULL,
  _include_notes BO<PERSON><PERSON>N DEFAULT TRUE,
  _include_deleted BOOLEAN DEFAULT FALSE
)

RETURNS TABLE(
  id UUID,
  name TEXT,
  created_by UUI<PERSON>,
  campaign_id UUID,
  icon TEXT,
  color TEXT,
  is_deleted BOOLEAN,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  notes JSONB
) AS $$


BEGIN
  RETURN QUERY
  SELECT
    f.id,
    f.name,
    f.created_by,
    f.campaign_id,
    f.icon,
    f.color,
    f.is_deleted,
    f.created_at,
    f.updated_at,
    CASE
      WHEN _include_notes THEN (
        SELECT COALESCE(JSONB_AGG(
          JSONB_BUILD_OBJECT(
            'id', n.id,
            'title', n.title,
            'content', n.content,
            'created_at', n.created_at,
            'updated_at', n.updated_at,
            'folder_id', n.folder_id,
            'created_by', n.created_by,
            'updated_by', n.updated_by,
            'campaign_id', n.campaign_id,
            'blocks', (
              SELECT COALESCE(JSONB_AGG(
                JSONB_BUILD_OBJECT(
                  'id', b.id,
                  'title', b.title,
                  'content', b.content,
                  'block_type', CASE
                    WHEN bt.id IS NOT NULL THEN JSONB_BUILD_OBJECT(
                      'id', bt.id,
                      'name', bt.name,
                      'description', bt.description,
                      'created_at', bt.created_at,
                      'updated_at', bt.updated_at
                    )
                    ELSE NULL
                  END,
                  'created_at', b.created_at,
                  'updated_at', b.updated_at,
                  'note_id', b.note_id,
                  'created_by', b.created_by,
                  'updated_by', b.updated_by
                )
              ) FILTER (WHERE b.id IS NOT NULL), '[]'::JSONB)
              FROM t_note_has_block nhb
              JOIN t_block b ON nhb.block_id = b.id
              LEFT JOIN t_block_type bt ON b.block_type = bt.id
              WHERE nhb.note_id = n.id
            )
          )
        ) FILTER (WHERE n.id IS NOT NULL), '[]'::JSONB)
        FROM t_note n
        WHERE n.folder_id = f.id
      )
      ELSE '[]'::JSONB
    END AS notes
  FROM t_folder f
  WHERE f.campaign_id = _campaign_id
    AND (_include_deleted OR f.is_deleted = FALSE)
    AND (_user_id IS NULL OR f.created_by = _user_id OR EXISTS (
      SELECT 1
      FROM t_campaign_has_player cp
      WHERE cp.campaign_id = f.campaign_id AND cp.player_id = _user_id
    ))
  ORDER BY f.created_at ASC;
END;

$$ LANGUAGE PLPGSQL SECURITY DEFINER;
