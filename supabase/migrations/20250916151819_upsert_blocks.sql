set check_function_bodies = on;

CREATE FUNCTION sp_upsert_block (
  _id UUID,
  _title TEXT,
  _content TEXT,
  _block_type UUID,
  _note_id UUID,
  _created_by UUID
)

RETURNS TABLE (
  id UUID,
  title TEXT,
  content TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  folder_id UUID,
  created_by UUI<PERSON>,
  updated_by UUI<PERSON>,
  campaign_id UUID,
  blocks JSONB[]
) AS $$

DECLARE
  _block_id UUID;
BEGIN
  IF _id IS NULL THEN
    INSERT INTO t_block (
      "title",
      "content",
      "block_type",
      "note_id",
      "created_by",
      "updated_at"
    ) VALUES (
      _title,
      _content,
      _block_type,
      _note_id,
      _created_by,
      NOW()
    )
    RETURNING t_block.id INTO _block_id;

    INSERT INTO t_note_has_block (
      "note_id",
      "block_id"
    ) VALUES (
      _note_id,
      _block_id
    );
  ELSE
    UPDATE t_block
    SET
      "title" = _title,
      "content" = _content,
      "block_type" = _block_type,
      "note_id" = _note_id,
      "updated_by" = _created_by,
      "updated_at" = NOW()
    WHERE t_block.id = _id
    RETURNING t_block.id INTO _block_id;
  END IF;

  RETURN QUERY
  SELECT
    n.id,
    n.title,
    n.content,
    n.created_at,
    n.updated_at,
    n.folder_id,
    n.created_by,
    n.updated_by,
    n.campaign_id,
    (
      SELECT COALESCE(JSONB_AGG(
        JSONB_BUILD_OBJECT(
          'id', b.id,
          'title', b.title,
          'content', b.content,
          'block_type', CASE
            WHEN bt.id IS NOT NULL THEN JSONB_BUILD_OBJECT(
              'id', bt.id,
              'name', bt.name,
              'description', bt.description,
              'created_at', bt.created_at,
              'updated_at', bt.updated_at
            )
            ELSE NULL
          END,
          'created_at', b.created_at,
          'updated_at', b.updated_at,
          'note_id', b.note_id,
          'created_by', b.created_by,
          'updated_by', b.updated_by
        )
      ) FILTER (WHERE b.id IS NOT NULL), '[]'::JSONB)
      FROM t_note_has_block nhb
      JOIN t_block b ON nhb.block_id = b.id
      LEFT JOIN t_block_type bt ON b.block_type = bt.id
      WHERE nhb.note_id = n.id
    ) AS blocks
  FROM t_note n
  WHERE n.id = _note_id;
END;

$$ LANGUAGE plpgsql SECURITY DEFINER;
